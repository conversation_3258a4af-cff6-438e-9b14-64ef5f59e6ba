import { CurrentUser } from "src/auth/types";
import { ReactorService } from "../reactor.service";
import * as Dto from "./dto";
export declare class ReactorController {
    private readonly reactorService;
    constructor(reactorService: ReactorService);
    getPosts(page: number, size: number, user: CurrentUser): Promise<{
        items: {
            id: string;
            author: {
                id: string;
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                avatar: string | null;
            };
            rating: {
                status: "like" | "dislike" | null;
                likes: number;
                dislikes: number;
            };
            usefulness: {
                value: number | null;
                count: number;
                totalValue: number | null;
            };
            title: {
                value: string;
                locale: "en" | "ru";
            }[];
            body: {
                value: string;
                locale: "en" | "ru";
            }[];
            tags: string[];
            createdAt: Date;
            updatedAt: Date;
        }[];
        total: number;
    }>;
    getPost(id: string, user: CurrentUser): Promise<{
        id: string;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        };
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
        title: {
            value: string;
            locale: "en" | "ru";
        }[];
        body: {
            value: string;
            locale: "en" | "ru";
        }[];
        tags: string[];
        createdAt: Date;
        updatedAt: Date;
    }>;
    createPost(body: Dto.CreatePost, user: CurrentUser): Promise<{
        id: string;
    }>;
    updatePost(id: string, body: Dto.UpdatePost, user: CurrentUser): Promise<boolean>;
    updatePostRating(id: string, body: Dto.UpdatePostRating, user: CurrentUser): Promise<{
        likes: number;
        dislikes: number;
        status: import(".prisma/client").$Enums.ReactorRatingType | null;
    }>;
    updatePostUsefulness(id: string, body: Dto.UpdatePostUsefulness, user: CurrentUser): Promise<{
        count: number;
        totalValue: number | null;
        value: number | null;
    }>;
    deletePost(id: string, body: Dto.DeletePost, user: CurrentUser): Promise<boolean>;
    getComments(entityType: Dto.GetCommentsEntityType, entityId: string, user: CurrentUser): Promise<{
        items: {
            path: string;
            id: string;
            author: {
                id: string;
                name: {
                    value: string;
                    locale: "en" | "ru";
                }[];
                avatar: string | null;
            } | null;
            rating: {
                status: "like" | "dislike" | null;
                likes: number;
                dislikes: number;
            };
            body: {
                value: string;
                locale: "en" | "ru";
            }[] | null;
            createdAt: Date;
            updatedAt: Date;
            isAnonymous: boolean;
            anonimityReason: string | null;
            deleteReason: string | null;
            deletedAt: Date | null;
            childrenCount: number;
        }[];
        total: number;
    }>;
    getComment(id: string, user: CurrentUser): Promise<{
        path: string;
        id: string;
        author: {
            id: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            avatar: string | null;
        } | null;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        body: {
            value: string;
            locale: "en" | "ru";
        }[] | null;
        createdAt: Date;
        updatedAt: Date;
        isAnonymous: boolean;
        anonimityReason: string | null;
        deleteReason: string | null;
        deletedAt: Date | null;
        childrenCount: number;
    }>;
    createComment(body: Dto.CreateComment, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateComment(id: string, body: Dto.UpdateComment, user: CurrentUser): Promise<boolean>;
    updateCommentRating(id: string, body: Dto.UpdateCommentRating, user: CurrentUser): Promise<{
        likes: number;
        dislikes: number;
        status: import(".prisma/client").$Enums.ReactorRatingType | null;
    }>;
    anonimifyComment(id: string, body: Dto.AnonimifyComment, user: CurrentUser): Promise<boolean>;
    deleteComment(id: string, body: Dto.DeleteComment, user: CurrentUser): Promise<boolean>;
}
